"""
Hierarchical Context Selector
Use system architecture to make intelligent context selection decisions.
"""

from typing import Dict, List, Any, Set, Tuple, Optional
from dataclasses import dataclass
from enum import Enum
import json

from .system_architecture_generator import SystemArchitectureGenerator, SystemArchitecture, ModuleCluster
from .intelligent_semantic_selector import IntelligentSemanticSelector
from .intelligent_context_models import QueryContext


class ContextSelectionStrategy(Enum):
    """Strategies for context selection."""
    WORKFLOW_FOCUSED = "workflow_focused"      # Focus on specific workflow
    ARCHITECTURE_OVERVIEW = "architecture_overview"  # Broad architectural view
    CLUSTER_DEEP_DIVE = "cluster_deep_dive"   # Deep dive into specific cluster
    CROSS_CUTTING = "cross_cutting"           # Cross-cutting concerns


@dataclass
class ArchitecturalContext:
    """Context selection based on architectural understanding."""
    strategy: ContextSelectionStrategy
    primary_clusters: List[str]
    supporting_clusters: List[str]
    workflow_path: List[str]
    architectural_rationale: str
    selected_entities: List[Dict[str, Any]]


class HierarchicalContextSelector:
    """Select context using hierarchical architectural understanding."""
    
    def __init__(self):
        self.architecture_generator = SystemArchitectureGenerator()
        self.semantic_selector = IntelligentSemanticSelector()
        self.system_architecture = None
    
    def select_hierarchical_context(self, ir_data: Dict[str, Any], user_query: str,
                                   focus_entities: List[str] = None,
                                   max_entities: int = 8) -> Dict[str, Any]:
        """
        Select context using hierarchical architectural understanding.
        
        Args:
            ir_data: IR data containing system information
            user_query: User's query
            focus_entities: Entities to focus on
            max_entities: Maximum entities to select
            
        Returns:
            Enhanced context with architectural understanding
        """
        print("🏗️ Starting Hierarchical Context Selection...")

        # Store focus entities for use in scoring
        self._current_focus_entities = focus_entities or []

        # Step 1: Generate system architecture if not cached
        if not self.system_architecture:
            print("   📊 Generating system architecture...")
            self.system_architecture = self.architecture_generator.generate_architecture(ir_data)
            print(f"   ✅ Architecture generated with {len(self.system_architecture.clusters)} clusters")

        # Step 2: Analyze query semantically
        print("   🧠 Analyzing query semantically...")
        query_analysis = self.semantic_selector.analyze_query(user_query)
        # Store original query for enhanced scoring
        query_analysis.original_query = user_query
        print(f"   ✅ Query intent: {query_analysis.intent.value}")
        
        # Step 3: Determine context selection strategy
        strategy = self._determine_selection_strategy(query_analysis, focus_entities)
        print(f"   🎯 Selection strategy: {strategy.value}")
        
        # Step 4: Select relevant clusters based on strategy
        architectural_context = self._select_architectural_context(
            strategy, query_analysis, focus_entities, max_entities
        )
        
        # Step 5: Select specific entities within chosen clusters
        selected_entities = self._select_entities_from_clusters(
            architectural_context, ir_data, query_analysis, max_entities, strategy
        )
        
        # Step 6: Generate architectural explanation
        explanation = self._generate_architectural_explanation(
            architectural_context, selected_entities, query_analysis
        )
        
        print(f"   ✅ Selected {len(selected_entities)} entities from {len(architectural_context.primary_clusters)} primary clusters")
        
        return {
            'strategy': strategy.value,
            'architectural_context': architectural_context,
            'selected_entities': selected_entities,
            'architectural_explanation': explanation,
            'system_architecture': self.system_architecture,
            'query_analysis': query_analysis
        }
    
    def _determine_selection_strategy(self, query_analysis: QueryContext,
                                    focus_entities: List[str] = None) -> ContextSelectionStrategy:
        """Determine the best context selection strategy."""
        intent = query_analysis.intent
        scope = query_analysis.scope
        
        # Strategy decision logic
        if intent.value in ['workflow_analysis', 'debugging_assistance']:
            return ContextSelectionStrategy.WORKFLOW_FOCUSED
        elif intent.value in ['architecture_understanding', 'system_overview']:
            return ContextSelectionStrategy.ARCHITECTURE_OVERVIEW
        elif scope.value == 'single_component' or (focus_entities and len(focus_entities) <= 2):
            return ContextSelectionStrategy.CLUSTER_DEEP_DIVE
        else:
            return ContextSelectionStrategy.CROSS_CUTTING
    
    def _select_architectural_context(self, strategy: ContextSelectionStrategy,
                                    query_analysis: QueryContext,
                                    focus_entities: List[str],
                                    max_entities: int) -> ArchitecturalContext:
        """Select architectural context based on strategy."""
        
        if strategy == ContextSelectionStrategy.WORKFLOW_FOCUSED:
            return self._select_workflow_context(query_analysis, focus_entities)
        elif strategy == ContextSelectionStrategy.ARCHITECTURE_OVERVIEW:
            return self._select_overview_context(query_analysis)
        elif strategy == ContextSelectionStrategy.CLUSTER_DEEP_DIVE:
            return self._select_cluster_context(query_analysis, focus_entities)
        else:  # CROSS_CUTTING
            return self._select_cross_cutting_context(query_analysis, focus_entities)
    
    def _select_workflow_context(self, query_analysis: QueryContext,
                               focus_entities: List[str]) -> ArchitecturalContext:
        """Select context for workflow-focused analysis."""
        # For workflow analysis, focus on core business logic and context processing
        primary_clusters = []

        # Always include core business logic
        core_clusters = [c.name for c in self.system_architecture.clusters
                        if c.module_type.value == 'core_business']
        primary_clusters.extend(core_clusters[:1])  # Top 1 core cluster

        # Include context processing for workflow analysis
        context_clusters = [c.name for c in self.system_architecture.clusters
                           if 'context' in c.name.lower()]
        primary_clusters.extend(context_clusters[:1])  # Top 1 context cluster

        # Supporting clusters based on query intent
        supporting_clusters = []
        if query_analysis.intent.value == 'security_analysis':
            # Add models/external for security workflows
            model_clusters = [c.name for c in self.system_architecture.clusters
                             if c.module_type.value == 'external_integration']
            supporting_clusters.extend(model_clusters[:1])
        else:
            # Add infrastructure for general workflows
            infra_clusters = [c.name for c in self.system_architecture.clusters
                             if c.module_type.value == 'infrastructure']
            supporting_clusters.extend(infra_clusters[:1])

        workflow_path = primary_clusters + supporting_clusters

        rationale = f"Selected core business logic and context processing clusters for {query_analysis.intent.value} workflow"

        return ArchitecturalContext(
            strategy=ContextSelectionStrategy.WORKFLOW_FOCUSED,
            primary_clusters=primary_clusters,
            supporting_clusters=supporting_clusters,
            workflow_path=workflow_path,
            architectural_rationale=rationale,
            selected_entities=[]
        )
    
    def _select_overview_context(self, query_analysis: QueryContext) -> ArchitecturalContext:
        """Select context for architectural overview."""
        # Select one cluster from each layer
        primary_clusters = []
        for layer, clusters in self.system_architecture.layer_hierarchy.items():
            if clusters:
                # Pick the most critical cluster from each layer
                layer_clusters = [c for c in self.system_architecture.clusters if c.name in clusters]
                if layer_clusters:
                    most_critical = max(layer_clusters, key=lambda c: c.criticality)
                    primary_clusters.append(most_critical.name)
        
        supporting_clusters = []
        workflow_path = primary_clusters
        
        rationale = f"Selected representative clusters from each architectural layer for system overview"
        
        return ArchitecturalContext(
            strategy=ContextSelectionStrategy.ARCHITECTURE_OVERVIEW,
            primary_clusters=primary_clusters,
            supporting_clusters=supporting_clusters,
            workflow_path=workflow_path,
            architectural_rationale=rationale,
            selected_entities=[]
        )
    
    def _select_cluster_context(self, query_analysis: QueryContext,
                              focus_entities: List[str]) -> ArchitecturalContext:
        """Select context for cluster deep dive."""
        # Find cluster that contains focus entities
        target_cluster = None
        if focus_entities:
            for cluster in self.system_architecture.clusters:
                if any(entity.lower() in ' '.join(cluster.key_entities).lower() 
                      for entity in focus_entities):
                    target_cluster = cluster
                    break
        
        if not target_cluster:
            # Default to most critical cluster
            target_cluster = max(self.system_architecture.clusters, key=lambda c: c.criticality)
        
        primary_clusters = [target_cluster.name]
        supporting_clusters = target_cluster.dependencies[:2]  # Top 2 dependencies
        
        workflow_path = [target_cluster.name] + supporting_clusters
        
        rationale = f"Deep dive into '{target_cluster.name}' cluster based on focus entities and criticality"
        
        return ArchitecturalContext(
            strategy=ContextSelectionStrategy.CLUSTER_DEEP_DIVE,
            primary_clusters=primary_clusters,
            supporting_clusters=supporting_clusters,
            workflow_path=workflow_path,
            architectural_rationale=rationale,
            selected_entities=[]
        )
    
    def _select_cross_cutting_context(self, query_analysis: QueryContext,
                                    focus_entities: List[str]) -> ArchitecturalContext:
        """Select context for cross-cutting concerns."""
        # Select clusters that span multiple layers for cross-cutting analysis
        primary_clusters = []

        # For cross-cutting, focus on utility and infrastructure clusters
        util_clusters = [c.name for c in self.system_architecture.clusters
                        if c.module_type.value == 'utility']
        primary_clusters.extend(util_clusters[:1])

        # Include infrastructure clusters
        infra_clusters = [c.name for c in self.system_architecture.clusters
                         if c.module_type.value == 'infrastructure' and c.name not in primary_clusters]
        primary_clusters.extend(infra_clusters[:2])  # Top 2 infrastructure clusters

        # Supporting clusters - add core for context
        supporting_clusters = []
        core_clusters = [c.name for c in self.system_architecture.clusters
                        if c.module_type.value == 'core_business']
        supporting_clusters.extend(core_clusters[:1])

        workflow_path = primary_clusters + supporting_clusters

        rationale = f"Selected utility and infrastructure clusters for cross-cutting {query_analysis.intent.value} analysis"

        return ArchitecturalContext(
            strategy=ContextSelectionStrategy.CROSS_CUTTING,
            primary_clusters=primary_clusters,
            supporting_clusters=supporting_clusters,
            workflow_path=workflow_path,
            architectural_rationale=rationale,
            selected_entities=[]
        )
    
    def _trace_workflow_path(self, clusters: List[str]) -> List[str]:
        """Trace workflow path through clusters."""
        # Simple implementation - could be more sophisticated
        return clusters
    
    def _select_entities_from_clusters(self, architectural_context: ArchitecturalContext,
                                     ir_data: Dict[str, Any], query_analysis: QueryContext,
                                     max_entities: int, strategy: ContextSelectionStrategy) -> List[Dict[str, Any]]:
        """REFACTORED: Select entities using GLOBAL scoring instead of per-cluster limits."""
        print("🔧 REFACTORED: Using global entity selection algorithm")

        # Get all clusters to search
        all_clusters = architectural_context.primary_clusters + architectural_context.supporting_clusters
        print(f"   🎯 Searching clusters: {all_clusters}")

        # Find modules in these clusters
        cluster_modules = {}
        for cluster in self.system_architecture.clusters:
            if cluster.name in all_clusters:
                cluster_modules[cluster.name] = cluster.modules

        # CRITICAL FIX: Collect ALL entities from ALL clusters first
        all_candidate_entities = []

        for cluster_name in all_clusters:
            modules = cluster_modules.get(cluster_name, [])
            cluster_entity_count = 0

            # Get entities from modules in this cluster
            for module in ir_data.get('modules', []):
                if module.get('name', '') in modules:
                    for entity in module.get('entities', []):
                        entity_with_metadata = entity.copy()
                        entity_with_metadata['cluster'] = cluster_name
                        entity_with_metadata['module_name'] = module.get('name', '')
                        entity_with_metadata['file_path'] = module.get('file', '')
                        all_candidate_entities.append(entity_with_metadata)
                        cluster_entity_count += 1

            print(f"   📊 {cluster_name}: {cluster_entity_count} entities")

        print(f"   📊 Total candidate entities: {len(all_candidate_entities)}")

        # NUCLEAR OPTION: Simple direct search for relevant entities
        print("   🔧 NUCLEAR OPTION: Direct entity search...")
        # Store IR data for semantic intelligence
        self._current_ir_data = ir_data

        # Extract key terms from query
        query_terms = self._extract_query_terms(query_analysis.original_query)
        print(f"   🔍 Query terms: {query_terms}")

        # Find entities that directly match query terms
        relevant_entities = self._find_directly_relevant_entities(all_candidate_entities, query_terms, ir_data)
        print(f"   ✅ Found {len(relevant_entities)} directly relevant entities")

        # If we found relevant entities, use them; otherwise use enhanced semantic analysis
        if relevant_entities:
            all_scored_entities = relevant_entities[:max_entities]
        else:
            print("   ⚠️ No direct matches, using enhanced semantic analysis...")
            # Fall back to enhanced semantic intelligence system
            all_scored_entities = self._score_entities_for_query(
                all_candidate_entities, query_analysis, strategy, "semantic_fallback"
            )[:max_entities]

        # CRITICAL FIX: Select top entities globally (no per-cluster limits!)
        top_entities = all_scored_entities[:max_entities]

        print(f"   ✅ Selected {len(top_entities)} top entities globally")

        # Show what was selected
        for i, entity in enumerate(top_entities[:5], 1):
            name = entity.get('name', 'unknown')
            score = entity.get('relevance_score', 0)
            cluster = entity.get('cluster', 'unknown')
            print(f"      {i}. {name} (score: {score:.2f}, cluster: {cluster})")

        return top_entities

    def _semantic_entity_intelligence(self, entity_name: str, entity_type: str,
                                    user_query: str, entity_file: str = "", ir_data: dict = None) -> float:
        """🧠 ENHANCED SEMANTIC INTELLIGENCE: Full IR-powered analysis"""

        # Ensure IR data is available
        if ir_data is None:
            ir_data = getattr(self, '_current_ir_data', {})

        # Extract query keywords for intelligent analysis
        query_keywords = self._extract_intelligent_keywords(user_query)
        query_intent = self._analyze_query_intent(user_query)

        # 🧠 STEP 1: DIRECT RELEVANCE SCORING (30% weight - reduced from 80%)
        direct_relevance = self._calculate_direct_relevance(entity_name, entity_type, entity_file, query_keywords, ir_data)

        # 🧠 STEP 2: RELATIONSHIP BOOST SCORING (25% weight - increased from 15%)
        relationship_boost = self._calculate_relationship_boost(entity_name, entity_type, query_keywords, ir_data)

        # 🧠 STEP 3: SEMANTIC RELEVANCE (25% weight - NEW! Uses IR concepts)
        semantic_match_score = self._compute_semantic_relevance(user_query, entity_name, entity_type, ir_data)

        # 🧠 STEP 4: CONTEXTUAL IMPORTANCE (10% weight - NEW! Uses IR complexity/criticality)
        contextual_importance_score = self._analyze_contextual_importance(entity_name, entity_type, ir_data)

        # 🧠 STEP 5: ARCHITECTURAL SIGNIFICANCE (5% weight - reduced from 3%)
        architectural_significance = self._calculate_architectural_significance(entity_name, entity_type, entity_file, ir_data)

        # 🧠 STEP 6: FILE CONTEXT (3% weight - NEW!)
        file_context_boost = self._analyze_file_context(entity_file, entity_name)

        # 🧠 STEP 7: CONTEXTUAL COHERENCE (2% weight - same)
        contextual_coherence = self._calculate_contextual_coherence(entity_name, entity_type, query_intent, ir_data)

        # REBALANCED: Give IR-driven semantic understanding more influence
        total_score = (
            direct_relevance * 0.30 +              # 30% - reduced weight on name matching
            relationship_boost * 0.25 +            # 25% - increased weight on relationships
            semantic_match_score * 0.25 +          # 25% - NEW: semantic concept matching
            contextual_importance_score * 0.10 +   # 10% - NEW: IR-based importance
            architectural_significance * 0.05 +    # 5% - slightly increased
            file_context_boost * 0.03 +            # 3% - NEW: file context
            contextual_coherence * 0.02            # 2% - same
        )

        print(f"   🧠 {entity_name}: direct={direct_relevance:.1f} rel={relationship_boost:.1f} sem={semantic_match_score:.1f} ctx_imp={contextual_importance_score:.1f} arch={architectural_significance:.1f} file={file_context_boost:.1f} ctx={contextual_coherence:.1f} = {total_score:.1f}")

        return max(total_score, 0.0)

    def _extract_intelligent_keywords(self, query: str) -> list:
        """🔧 FIXED: Extract keywords that actually matter"""
        import re

        # Remove useless words but keep important ones
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by'}

        # Keep question words and action words - they're important!
        important_words = {'how', 'what', 'where', 'when', 'why', 'does', 'is', 'are', 'can', 'will', 'would', 'should',
                          'apply', 'edit', 'file', 'chat', 'loop', 'work', 'handle', 'manage', 'process', 'run',
                          'git', 'error', 'class', 'function', 'method', 'watch', 'monitor', 'detect', 'change'}

        # Extract all words
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', query.lower())

        # Filter and prioritize
        keywords = []
        for word in words:
            if len(word) > 2 and word not in stop_words:
                keywords.append(word)
            elif word in important_words:
                keywords.append(word)

        # Add compound terms (like "apply_edits", "file_editor")
        compound_terms = re.findall(r'\b[a-z]+[_-][a-z]+\b', query.lower())
        keywords.extend(compound_terms)

        # Remove duplicates while preserving order
        seen = set()
        unique_keywords = []
        for kw in keywords:
            if kw not in seen:
                seen.add(kw)
                unique_keywords.append(kw)

        return unique_keywords[:15]  # Return more keywords for better matching

    def _analyze_query_intent(self, query: str) -> str:
        """🧠 Detect query intent for strategy selection"""
        query_lower = query.lower()

        intent_patterns = {
            'architecture': ['architecture', 'structure', 'design', 'overview', 'organization'],
            'workflow': ['how does', 'process', 'workflow', 'flow', 'sequence', 'steps'],
            'implementation': ['implement', 'work', 'function', 'method', 'class'],
            'debugging': ['error', 'bug', 'issue', 'problem', 'fail', 'exception'],
            'integration': ['integrate', 'connect', 'api', 'interface', 'external'],
            'data_flow': ['data', 'flow', 'transfer', 'process', 'transform']
        }

        for intent, patterns in intent_patterns.items():
            if any(pattern in query_lower for pattern in patterns):
                return intent

        return 'general'

    def _calculate_direct_relevance(self, entity_name: str, entity_type: str, entity_file: str,
                                  query_keywords: list, ir_data: dict) -> float:
        """🔧 FINAL FIX: Prioritize functions/classes over variables"""
        score = 0.0
        entity_lower = entity_name.lower()

        # CRITICAL FIX: Entity type multipliers (functions/classes are more important than variables)
        type_multipliers = {
            'function': 3.0,    # Functions are 3x more important
            'method': 3.0,      # Methods are 3x more important
            'class': 2.5,       # Classes are 2.5x more important
            'variable': 0.3,    # Variables are much less important
            'constant': 0.5     # Constants are somewhat less important
        }

        base_multiplier = type_multipliers.get(entity_type, 1.0)

        # STEP 1: Direct name matching with type awareness
        for keyword in query_keywords:
            kw_lower = keyword.lower()

            # Skip useless keywords
            if kw_lower in ['how', 'does', 'the', 'what', 'is', 'are']:
                continue

            # Exact match = jackpot (but scaled by entity type)
            if entity_lower == kw_lower:
                boost = 500 * base_multiplier  # Reduced from 1000
                score += boost
                print(f"      🎯 EXACT: {entity_name} ({entity_type}) == '{keyword}' +{boost:.1f}")
                continue

            # Contains match = very good (scaled by entity type)
            if kw_lower in entity_lower:
                match_strength = len(kw_lower) / len(entity_lower)
                boost = 100 * match_strength * base_multiplier
                score += boost
                print(f"      🎯 CONTAINS: {entity_name} ({entity_type}) contains '{keyword}' +{boost:.1f}")
                continue

            # Fuzzy matching for common variations
            if self._fuzzy_match(entity_lower, kw_lower):
                boost = 50 * base_multiplier
                score += boost
                print(f"      🎯 FUZZY: {entity_name} ({entity_type}) ~ '{keyword}' +{boost:.1f}")

        # STEP 2: Smart query understanding (only for functions/methods/classes)
        if entity_type in ['function', 'method', 'class']:
            query_text = ' '.join(query_keywords).lower()

            # "apply edits" should find apply_edits, edit_files, etc.
            if 'apply' in query_text and 'edit' in query_text:
                if any(term in entity_lower for term in ['apply', 'edit', 'update', 'modify']):
                    score += 400
                    print(f"      🎯 EDIT OPERATION: {entity_name} +400")

            # "chat loop" should find run, loop, chat, message functions
            if 'chat' in query_text and 'loop' in query_text:
                if any(term in entity_lower for term in ['run', 'loop', 'chat', 'message', 'send']):
                    score += 400
                    print(f"      🎯 CHAT OPERATION: {entity_name} +400")

            # "file" operations
            if 'file' in query_text:
                if any(term in entity_lower for term in ['file', 'read', 'write', 'save', 'load']):
                    score += 300
                    print(f"      🎯 FILE OPERATION: {entity_name} +300")

            # "git" operations
            if 'git' in query_text:
                if any(term in entity_lower for term in ['git', 'commit', 'repo', 'branch']):
                    score += 400
                    print(f"      🎯 GIT OPERATION: {entity_name} +400")

        # STEP 3: Penalize irrelevant variables heavily
        if entity_type == 'variable':
            # Variables like "classes", "handler" should not dominate
            if entity_lower in ['classes', 'handler', 'files', 'edits']:
                score *= 0.1  # Massive penalty
                print(f"      ❌ VARIABLE PENALTY: {entity_name} is just a variable -90%")

        return score

    def _extract_query_terms(self, query: str) -> list:
        """🔧 NUCLEAR OPTION: Extract key terms that should match entity names"""
        import re

        # Convert query to lowercase for processing
        query_lower = query.lower()

        # Define key operation patterns and their target entities
        operation_patterns = {
            'apply.*edit': ['apply_edits', 'apply_edit', 'edit_files', 'do_edit'],
            'chat.*loop': ['run', 'chat_loop', 'send_message', 'get_input', 'main_loop'],
            'git.*operation': ['GitRepo', 'git_add', 'git_commit', 'commit', 'repo'],
            'file.*process': ['process_file', 'file_handler', 'read_file', 'write_file'],
            'error.*handle': ['handle_error', 'exception', 'error_handler', 'catch_error']
        }

        # Check for operation patterns
        target_entities = []
        for pattern, entities in operation_patterns.items():
            if re.search(pattern, query_lower):
                target_entities.extend(entities)
                print(f"   🎯 Pattern '{pattern}' matched, targeting: {entities}")

        # Extract individual words as backup
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', query_lower)
        important_words = [w for w in words if len(w) > 3 and w not in
                          ['does', 'aider', 'work', 'what', 'how', 'the', 'and', 'or']]

        # Combine targeted entities with important words
        all_terms = target_entities + important_words

        # Remove duplicates while preserving order
        seen = set()
        unique_terms = []
        for term in all_terms:
            if term not in seen:
                seen.add(term)
                unique_terms.append(term)

        return unique_terms[:20]  # Limit to top 20 terms

    def _find_directly_relevant_entities(self, candidate_entities: list, query_terms: list, ir_data: dict) -> list:
        """🔧 NUCLEAR OPTION: Find entities that directly match query terms"""
        relevant_entities = []

        for entity in candidate_entities:
            entity_name = entity.get('name', '').lower()
            entity_type = entity.get('type', '')

            # Skip variables unless they're really important
            if entity_type == 'variable':
                continue

            # Calculate relevance score
            relevance_score = 0
            matches = []

            for term in query_terms:
                term_lower = term.lower()

                # Exact match = highest priority
                if entity_name == term_lower:
                    relevance_score += 1000
                    matches.append(f"EXACT:{term}")

                # Contains match = high priority
                elif term_lower in entity_name:
                    relevance_score += 500
                    matches.append(f"CONTAINS:{term}")

                # Reverse contains = medium priority
                elif entity_name in term_lower:
                    relevance_score += 300
                    matches.append(f"REVERSE:{term}")

            # Boost functions and classes
            if entity_type in ['function', 'method']:
                relevance_score *= 2
            elif entity_type == 'class':
                relevance_score *= 1.5

            # Only include entities with some relevance
            if relevance_score > 0:
                entity_copy = entity.copy()
                entity_copy['relevance_score'] = relevance_score
                entity_copy['matches'] = matches
                relevant_entities.append(entity_copy)
                print(f"   ✅ {entity_name} ({entity_type}): {relevance_score} - {matches}")

        # Sort by relevance score (highest first)
        relevant_entities.sort(key=lambda e: e['relevance_score'], reverse=True)

        return relevant_entities

    def _simple_keyword_fallback(self, candidate_entities: list, user_query: str, max_entities: int) -> list:
        """🔧 Simple keyword fallback without complex IR analysis"""
        import re

        # Extract simple keywords from query
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', user_query.lower())
        keywords = [w for w in words if len(w) > 3 and w not in ['does', 'aider', 'work', 'what', 'how', 'the']]

        scored_entities = []

        for entity in candidate_entities:
            entity_name = entity.get('name', '').lower()
            entity_type = entity.get('type', '')

            # Skip variables
            if entity_type == 'variable':
                continue

            score = 0
            for keyword in keywords:
                if keyword in entity_name:
                    score += 100

            if score > 0:
                entity_copy = entity.copy()
                entity_copy['relevance_score'] = score
                scored_entities.append(entity_copy)

        # Sort by score and return top entities
        scored_entities.sort(key=lambda e: e['relevance_score'], reverse=True)
        return scored_entities[:max_entities]

    def _fuzzy_match(self, entity_name: str, keyword: str) -> bool:
        """Simple fuzzy matching for common variations"""
        # Handle common variations
        variations = {
            'edit': ['edits', 'editing', 'editor'],
            'file': ['files', 'filename'],
            'apply': ['applies', 'applied'],
            'process': ['processes', 'processing', 'processor'],
            'handle': ['handles', 'handling', 'handler'],
            'manage': ['manages', 'managing', 'manager'],
            'watch': ['watches', 'watching', 'watcher'],
            'request': ['requests', 'requesting']
        }

        # Check if keyword matches any variation of entity name parts
        for base, vars in variations.items():
            if base in keyword and any(var in entity_name for var in vars):
                return True
            if base in entity_name and any(var in keyword for var in vars):
                return True

        return False

    def _calculate_relationship_boost(self, entity_name: str, entity_type: str,
                                    query_keywords: list, ir_data: dict) -> float:
        """🔗 CORE INSIGHT: Boost entities with connected relevant entities"""
        if not ir_data:
            return 0.0

        boost_score = 0.0
        entity_info = self._find_entity_in_ir(entity_name, ir_data)

        if not entity_info:
            return 0.0

        # R2.1: Class-Method Clustering (YOUR BRILLIANT INSIGHT!)
        if entity_type == "class":
            # Find methods in this class that also match keywords
            class_methods = []
            for module in ir_data.get('modules', []):
                for entity in module.get('entities', []):
                    if (entity.get('type') == 'method' and
                        entity.get('class_name') == entity_name):
                        class_methods.append(entity)

            # Check how many methods match our keywords
            matching_methods = []
            for method in class_methods:
                method_name = method.get('name', '')
                if any(kw.lower() in method_name.lower() for kw in query_keywords):
                    matching_methods.append(method_name)

            # Boost class score based on relevant methods
            if matching_methods:
                boost = len(matching_methods) * 30
                boost_score += boost
                print(f"      🔗 CLASS-METHOD CLUSTER: {entity_name} has {len(matching_methods)} relevant methods +{boost}")

                # If class has many relevant methods, it's probably central to the query
                if len(matching_methods) > 2:
                    hub_bonus = 50
                    boost_score += hub_bonus
                    print(f"      🔗 HUB CLASS BONUS: {entity_name} is central hub +{hub_bonus}")

        # R2.2: Function Call Relationships
        if entity_type in ["function", "method"]:
            # Check what this function calls
            called_functions = entity_info.get('calls', [])
            calling_functions = entity_info.get('used_by', [])

            # Boost if called functions also match keywords
            relevant_calls = sum(1 for func_name in called_functions
                               if any(kw.lower() in func_name.lower() for kw in query_keywords))
            if relevant_calls > 0:
                boost = relevant_calls * 20
                boost_score += boost
                print(f"      🔗 CALLS RELEVANT: {entity_name} calls {relevant_calls} relevant functions +{boost}")

            # Boost if calling functions also match keywords
            relevant_callers = sum(1 for func_name in calling_functions
                                 if any(kw.lower() in func_name.lower() for kw in query_keywords))
            if relevant_callers > 0:
                boost = relevant_callers * 15
                boost_score += boost
                print(f"      🔗 CALLED BY RELEVANT: {entity_name} called by {relevant_callers} relevant functions +{boost}")

        # R2.3: Module/Package Coherence
        entity_module = entity_info.get('module', '')
        if entity_module:
            same_module_entities = []
            for module in ir_data.get('modules', []):
                if module.get('name') == entity_module:
                    same_module_entities = module.get('entities', [])
                    break

            relevant_siblings = [e for e in same_module_entities
                               if any(kw.lower() in e.get('name', '').lower() for kw in query_keywords)]

            if len(relevant_siblings) > 1:  # Multiple relevant entities in same module
                boost = len(relevant_siblings) * 10
                boost_score += boost
                print(f"      🔗 MODULE COHERENCE: {entity_name} in module with {len(relevant_siblings)} relevant entities +{boost}")

        return boost_score

    def _find_entity_in_ir(self, entity_name: str, ir_data: dict) -> dict:
        """🔍 Helper to find entity information in IR data"""
        if not ir_data:
            return None

        for module in ir_data.get('modules', []):
            for entity in module.get('entities', []):
                if entity.get('name') == entity_name:
                    return entity
        return None

    def _analyze_entity_relationships(self, entity_name: str, ir_data: dict) -> float:
        """🔗 DEPRECATED: Replaced by _calculate_relationship_boost"""
        # This method is kept for backward compatibility but not used
        return 0.0

    def _calculate_architectural_significance(self, entity_name: str, entity_type: str,
                                            entity_file: str, ir_data: dict) -> float:
        """🏗️ Calculate architectural significance using codebase importance detection"""
        significance_score = 0.0
        entity_info = self._find_entity_in_ir(entity_name, ir_data)

        # R3.1: Usage Frequency (how often entity is referenced)
        if entity_info:
            usage_count = len(entity_info.get('used_by', [])) + len(entity_info.get('calls', []))
            if usage_count > 10:
                significance_score += 40
                print(f"      🏗️ HIGH USAGE: {entity_name} usage_count={usage_count} +40")
            elif usage_count > 5:
                significance_score += 25
                print(f"      🏗️ MODERATE USAGE: {entity_name} usage_count={usage_count} +25")
            elif usage_count > 2:
                significance_score += 10
                print(f"      🏗️ LOW USAGE: {entity_name} usage_count={usage_count} +10")

        # R3.2: Centrality in call graph
        if entity_type == "class" and ir_data:
            # Classes with many methods are often central
            method_count = 0
            for module in ir_data.get('modules', []):
                for entity in module.get('entities', []):
                    if (entity.get('type') == 'method' and
                        entity.get('class_name') == entity_name):
                        method_count += 1

            if method_count > 8:
                significance_score += 30
                print(f"      🏗️ LARGE CLASS: {entity_name} has {method_count} methods +30")
            elif method_count > 4:
                significance_score += 15
                print(f"      🏗️ MEDIUM CLASS: {entity_name} has {method_count} methods +15")

        # R3.3: File/Module importance indicators
        if entity_file:
            file_indicators = {
                'main.py': 50, '__init__.py': 40, 'app.py': 40,
                'core': 35, 'base': 30, 'manager': 25, 'handler': 25,
                'service': 20, 'utils': 15, 'helper': 10
            }

            for indicator, score in file_indicators.items():
                if indicator in entity_file.lower():
                    significance_score += score
                    print(f"      🏗️ FILE IMPORTANCE: {entity_name} in {indicator} file +{score}")
                    break

        # R3.4: Interface/Abstract class bonus
        if entity_type == "class":
            if any(keyword in entity_name.lower()
                   for keyword in ['interface', 'abstract', 'base', 'manager', 'service']):
                significance_score += 25
                print(f"      🏗️ ARCHITECTURAL CLASS: {entity_name} is architectural component +25")

        # R3.5: Exception/Error handling significance
        if any(keyword in entity_name.lower()
               for keyword in ['error', 'exception', 'handler']):
            significance_score += 20
            print(f"      🏗️ ERROR HANDLING: {entity_name} handles errors +20")

        return significance_score

    def _calculate_contextual_coherence(self, entity_name: str, entity_type: str,
                                      query_intent: str, ir_data: dict) -> float:
        """🧠 Calculate contextual coherence for selection harmony"""
        coherence_score = 0.0

        # R4.1: Query intent alignment
        intent_bonuses = {
            'architecture': {'class': 15, 'interface': 20},
            'workflow': {'function': 15, 'method': 15},
            'implementation': {'method': 10, 'function': 10},
            'debugging': {'function': 20, 'method': 20},
            'integration': {'class': 15, 'interface': 25}
        }

        if query_intent in intent_bonuses and entity_type in intent_bonuses[query_intent]:
            bonus = intent_bonuses[query_intent][entity_type]
            coherence_score += bonus
            print(f"      🧠 INTENT ALIGNMENT: {entity_name} matches {query_intent} intent +{bonus}")

        # R4.2: Workflow coherence
        if query_intent == 'workflow':
            workflow_indicators = ['create', 'validate', 'process', 'save', 'send', 'handle']
            if any(indicator in entity_name.lower() for indicator in workflow_indicators):
                coherence_score += 15
                print(f"      🧠 WORKFLOW ENTITY: {entity_name} is workflow component +15")

        return coherence_score

    def _compute_semantic_relevance(self, user_query: str, entity_name: str, entity_type: str, ir_data: dict) -> float:
        """🧠 Compute semantic relevance between query and entity using IR context"""
        score = 0.0

        # 🧠 EXTRACT DOMAIN CONCEPTS FROM QUERY
        query_concepts = self._extract_query_concepts(user_query)

        # 🧠 EXTRACT ENTITY DOMAIN FROM IR DATA
        entity_domain = self._extract_entity_domain(entity_name, ir_data)

        # 🧠 SEMANTIC MATCHING: How well do query concepts match entity domain?
        concept_overlap = len(set(query_concepts) & set(entity_domain))
        if concept_overlap > 0:
            score += concept_overlap * 20.0
            print(f"   🧠 CONCEPT MATCH: {entity_name} shares {concept_overlap} concepts +{concept_overlap * 20}")

        # 🧠 QUERY INTENT ANALYSIS: What type of understanding does the query need?
        query_intent = self._analyze_query_intent(user_query)
        entity_capability = self._analyze_entity_capability(entity_name, entity_type, ir_data)

        if query_intent == entity_capability:
            score += 25.0
            print(f"   🧠 INTENT MATCH: {entity_name} matches {query_intent} intent +25")

        return score

    def _analyze_contextual_importance(self, entity_name: str, entity_type: str, ir_data: dict) -> float:
        """🧠 Analyze contextual importance from codebase patterns"""
        score = 0.0

        if not ir_data:
            return score

        # 🧠 FIND ENTITY IN IR DATA
        entity_info = None
        for module in ir_data.get('modules', []):
            for entity in module.get('entities', []):
                if entity.get('name') == entity_name:
                    entity_info = entity
                    break
            if entity_info:
                break

        if not entity_info:
            return score

        # 🧠 COMPLEXITY ANALYSIS: More complex entities are often more important
        complexity = entity_info.get('complexity', 0)
        if complexity > 10:
            score += 15.0
            print(f"   🧠 HIGH COMPLEXITY: {entity_name} complexity={complexity} +15")
        elif complexity > 5:
            score += 8.0
            print(f"   🧠 MODERATE COMPLEXITY: {entity_name} complexity={complexity} +8")

        # 🧠 SIDE EFFECTS ANALYSIS: Entities with side effects are often core business logic
        side_effects = entity_info.get('side_effects', [])
        if side_effects and 'none' not in side_effects:
            score += 10.0
            print(f"   🧠 HAS SIDE EFFECTS: {entity_name} has {len(side_effects)} side effects +10")

        # 🧠 CRITICALITY SCORE: Use existing criticality if available
        criticality = entity_info.get('criticality_score', 0)
        if criticality > 5:
            score += criticality * 2
            print(f"   🧠 HIGH CRITICALITY: {entity_name} criticality={criticality} +{criticality * 2}")

        return score

    def _extract_query_concepts(self, query: str) -> list:
        """🧠 Extract domain concepts from query without hardcoded keywords"""
        import re

        # Remove common words and extract meaningful terms
        stop_words = {'how', 'does', 'the', 'system', 'what', 'is', 'are', 'can', 'will', 'would', 'should'}

        # Extract words that could be domain concepts
        words = re.findall(r'\b[a-zA-Z]+\b', query.lower())
        concepts = [word for word in words if word not in stop_words and len(word) > 2]

        return concepts

    def _extract_entity_domain(self, entity_name: str, ir_data: dict) -> list:
        """🧠 Extract domain concepts from entity name and context"""
        import re

        # Extract concepts from entity name (camelCase/PascalCase splitting)
        name_parts = re.findall(r'[A-Z][a-z]*|[a-z]+', entity_name)
        domain_concepts = [part.lower() for part in name_parts if len(part) > 2]

        # TODO: Could also extract from file path, method names, etc.

        return domain_concepts

    def _analyze_query_intent(self, query: str) -> str:
        """🧠 Analyze what the query is trying to understand"""
        query_lower = query.lower()

        if any(word in query_lower for word in ['how', 'process', 'work', 'flow']):
            return 'process_understanding'
        elif any(word in query_lower for word in ['what', 'which', 'show']):
            return 'entity_identification'
        elif any(word in query_lower for word in ['manage', 'handle', 'control']):
            return 'management_understanding'
        else:
            return 'general_understanding'

    def _analyze_entity_capability(self, entity_name: str, entity_type: str, ir_data: dict) -> str:
        """🧠 Analyze what this entity is capable of providing"""
        if entity_type == 'class':
            # Classes typically provide process understanding
            return 'process_understanding'
        elif entity_type == 'function':
            # Functions typically provide specific operations
            return 'entity_identification'
        else:
            # Variables typically provide references
            return 'general_understanding'

    def _analyze_query_intent_semantics(self, query: str, entity_name: str, entity_type: str) -> float:
        """🧠 Analyze what the query semantically needs without keyword matching"""
        import re

        # Semantic query analysis patterns
        management_queries = [r'how.*(manage|handle|process|work)', r'what.*(does|handles|manages)']
        workflow_queries = [r'(flow|process|workflow|pipeline|steps)', r'how.*(system|process)']
        creation_queries = [r'(create|build|generate|make|produce)', r'how.*(create|build)']

        boost = 0

        # If query asks about management/handling, prioritize action-oriented entities
        for pattern in management_queries:
            if re.search(pattern, query, re.IGNORECASE):
                if re.search(r'(Manager|Handler|Processor|Controller)', entity_name, re.IGNORECASE):
                    boost += 40
                    print(f"   🎯 MANAGEMENT QUERY MATCH: {entity_name} +40")
                break

        # If query asks about workflows, prioritize process entities
        for pattern in workflow_queries:
            if re.search(pattern, query, re.IGNORECASE):
                if re.search(r'(Runner|Executor|Engine|Processor)', entity_name, re.IGNORECASE):
                    boost += 35
                    print(f"   � WORKFLOW QUERY MATCH: {entity_name} +35")
                break

        # If query asks about creation, prioritize builder entities
        for pattern in creation_queries:
            if re.search(pattern, query, re.IGNORECASE):
                if re.search(r'(Creator|Builder|Generator|Factory)', entity_name, re.IGNORECASE):
                    boost += 35
                    print(f"   🏗️ CREATION QUERY MATCH: {entity_name} +35")
                break

        return boost

    def _analyze_file_context(self, file_path: str, entity_name: str) -> float:
        """🧠 Analyze file context to understand entity importance"""

        if not file_path:
            return 0

        boost = 0

        # Core business logic files are more valuable
        if any(indicator in file_path.lower() for indicator in ['manager', 'service', 'handler', 'processor']):
            boost += 10
            print(f"   � BUSINESS FILE: {entity_name} +10")

        # Infrastructure files are less valuable
        elif any(indicator in file_path.lower() for indicator in ['database', 'config', 'util', 'helper']):
            boost -= 5
            print(f"   📁 INFRASTRUCTURE FILE: {entity_name} -5")

        # Main files often contain setup, not core logic
        elif 'main.py' in file_path.lower():
            boost -= 10
            print(f"   📁 MAIN FILE: {entity_name} -10")

        return boost

    def _score_entities_for_query(self, entities: List[Dict[str, Any]],
                                query_analysis: QueryContext,
                                strategy: ContextSelectionStrategy = None,
                                cluster_name: str = None) -> List[Dict[str, Any]]:
        """ENHANCED Score entities based on query relevance and architectural strategy."""
        import re

        # Methods to PENALIZE (they're selected too often but are not core functionality)
        PENALIZED_METHODS = {
            'get_parser': -5.0, 'get_md_help': -5.0, '__init__': -3.0, 'main': -2.0
        }

        scored_entities = []
        user_query = getattr(query_analysis, 'original_query', '').lower()
        focus_entities = getattr(self, '_current_focus_entities', [])

        for entity in entities:
            entity_name = entity.get('name', '')
            entity_type = entity.get('type', '')
            entity_file = entity.get('file', '')

            # 🧠 PRIMARY: SEMANTIC INTELLIGENCE (True AI-model thinking!)
            print(f"   🧠 Semantic analysis: {entity_name} ({entity_type})")
            # Pass IR data for true semantic analysis
            ir_data = getattr(self, '_current_ir_data', None)
            score = self._semantic_entity_intelligence(entity_name, entity_type, user_query, entity_file, ir_data)

            # 1. CRITICAL: Focus entities get additional boost
            if focus_entities:
                for focus_entity in focus_entities:
                    if focus_entity.lower() in entity_name.lower():
                        score += 30.0  # Additional boost on top of semantic scoring
                        print(f"   🎯 FOCUS BOOST: {entity_name} +30.0")

            # 4. PENALTY: Penalize over-selected irrelevant methods
            if entity_name in PENALIZED_METHODS:
                penalty = PENALIZED_METHODS[entity_name]
                score += penalty
                print(f"   ❌ PENALTY: {entity_name} {penalty}")

            # 5. Keyword matching boost
            if user_query:
                query_keywords = re.findall(r'\b\w+\b', user_query)
                for keyword in query_keywords:
                    if keyword in entity_name:
                        score += 5.0

            # 6. Type-based scoring
            if entity_type == 'class':
                score += 2.0  # Classes are generally important
            elif entity_type in ['method', 'function']:
                score += 1.0  # Methods are important

            # 7. Criticality from IR data
            criticality = entity.get('criticality_score', 0)
            score += criticality * 0.1  # Small boost from IR criticality

            # 8. Strategy-specific adjustments (reduced impact)
            if strategy == ContextSelectionStrategy.ARCHITECTURE_OVERVIEW and entity_type == 'class':
                score += 1.0
            elif strategy == ContextSelectionStrategy.WORKFLOW_FOCUSED and entity_type in ['function', 'method']:
                score += 1.0

            entity['relevance_score'] = score
            scored_entities.append(entity)

        # Sort by score
        scored_entities.sort(key=lambda e: e.get('relevance_score', 0.0), reverse=True)

        return scored_entities
    
    def _rank_final_selection(self, entities: List[Dict[str, Any]],
                            query_analysis: QueryContext, max_entities: int) -> List[Dict[str, Any]]:
        """DEPRECATED: Final ranking now done in global selection."""
        # This method is no longer needed since we do global selection
        # Just return the entities as-is (they're already sorted by score)
        return entities[:max_entities]
    
    def _generate_architectural_explanation(self, architectural_context: ArchitecturalContext,
                                          selected_entities: List[Dict[str, Any]],
                                          query_analysis: QueryContext) -> str:
        """Generate explanation of architectural context selection."""
        
        explanation = f"""🏗️ Architectural Context Selection

🎯 Strategy: {architectural_context.strategy.value.replace('_', ' ').title()}
📋 Rationale: {architectural_context.architectural_rationale}

🏛️ Primary Clusters: {', '.join(architectural_context.primary_clusters)}
🔗 Supporting Clusters: {', '.join(architectural_context.supporting_clusters)}
🔄 Workflow Path: {' → '.join(architectural_context.workflow_path)}

📊 Selected Components:
"""
        
        # Group entities by cluster
        entities_by_cluster = {}
        for entity in selected_entities:
            cluster = entity.get('cluster', 'unknown')
            if cluster not in entities_by_cluster:
                entities_by_cluster[cluster] = []
            entities_by_cluster[cluster].append(entity)
        
        for cluster, entities in entities_by_cluster.items():
            explanation += f"   • {cluster}: {len(entities)} components\n"
            for entity in entities[:3]:  # Show top 3
                name = entity.get('name', 'unknown')
                score = entity.get('relevance_score', 0.0)
                explanation += f"     - {name} (score: {score:.3f})\n"
        
        explanation += f"""
🧠 Query Analysis:
   • Intent: {query_analysis.intent.value}
   • Scope: {query_analysis.scope.value}
   • Confidence: {query_analysis.confidence:.2f}

This selection provides architectural context that spans the relevant system layers
and components needed to understand your query in the context of the overall system design."""
        
        return explanation
